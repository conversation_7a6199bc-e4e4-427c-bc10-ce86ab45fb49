#!/usr/bin/env python3
"""
数据库迁移脚本：为ForumPost表添加is_feedback字段
"""

from app import app, db
from models import ForumPost

def migrate_database():
    """执行数据库迁移"""
    with app.app_context():
        try:
            # 检查是否已经存在is_feedback字段
            inspector = db.inspect(db.engine)
            columns = [col['name'] for col in inspector.get_columns('forum_post')]
            
            if 'is_feedback' not in columns:
                print("正在添加is_feedback字段...")
                # 添加新字段
                db.engine.execute('ALTER TABLE forum_post ADD COLUMN is_feedback BOOLEAN DEFAULT FALSE')
                print("is_feedback字段添加成功！")
            else:
                print("is_feedback字段已存在，跳过迁移。")
                
        except Exception as e:
            print(f"迁移失败: {e}")
            return False
            
    return True

if __name__ == '__main__':
    print("开始数据库迁移...")
    if migrate_database():
        print("数据库迁移完成！")
    else:
        print("数据库迁移失败！")
